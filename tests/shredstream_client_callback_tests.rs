use std::time::Duration;

use shreder::common::client::{
    ShredstreamClient, ShredstreamClientConfig,
    config::{CircuitBreakerConfig, RetryConfig},
};

mod mock_server;
mod test_helpers;

use mock_server::{MockServerConfig, start_mock_server};
use test_helpers::*;

#[tokio::test]
async fn test_k1_disconnect_callback_parameters() {
    let disconnect_capture = CallbackCapture::<DisconnectCallbackData>::new();

    let config = MockServerConfig::multiple_failures_then_success_config(3);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let client_config = ShredstreamClientConfig {
        retry_config: Some(RetryConfig { enabled: true, max_attempts: 5, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }),
        disconnect_callback: Some(disconnect_capture.create_disconnect_callback()),
        ..Default::default()
    };

    let mut client = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(client_config));

    let result = client.subscribe().await;

    assert!(result.is_ok(), "Expected successful subscription");

    let disconnect_calls = disconnect_capture.get_calls();
    assert!(disconnect_calls.len() >= 3, "Expected at least 3 disconnect callbacks");

    for (i, call) in disconnect_calls.iter().enumerate() {
        assert_eq!(call.endpoint, server.endpoint(), "Endpoint should match");
        assert!(!call.error_message.is_empty(), "Error message should not be empty");
        assert!(call.delay.as_millis() > 0, "Delay should be positive");
        assert_eq!(call.attempt, (i + 1) as u32, "Attempt number should be correct");
    }

    server.stop().await;
}

#[tokio::test]
async fn test_k2_success_callback_conditions() {
    let retry_success_capture = CallbackCapture::<RetrySuccessCallbackData>::new();

    let success_config = MockServerConfig::success_config(3, 10);
    let success_server = start_mock_server(success_config).await.expect("Failed to start success server");

    let client_config = ShredstreamClientConfig { retry_success_callback: Some(retry_success_capture.create_retry_success_callback()), ..Default::default() };

    let mut client = ShredstreamClient::new(success_server.endpoint(), create_test_filters(), Some(client_config));

    let result = client.subscribe().await;

    assert!(result.is_ok(), "Expected successful subscription");

    let success_calls = retry_success_capture.get_calls();
    assert_eq!(success_calls.len(), 0, "No success callback should be called for first-time success");

    success_server.stop().await;

    let retry_success_capture2 = CallbackCapture::<RetrySuccessCallbackData>::new();

    let retry_config = MockServerConfig::multiple_failures_then_success_config(2);
    let retry_server = start_mock_server(retry_config).await.expect("Failed to start retry server");

    let client_config2 = ShredstreamClientConfig {
        retry_config: Some(RetryConfig { enabled: true, max_attempts: 5, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }),
        retry_success_callback: Some(retry_success_capture2.create_retry_success_callback()),
        ..Default::default()
    };

    let mut client2 = ShredstreamClient::new(retry_server.endpoint(), create_test_filters(), Some(client_config2));

    let result2 = client2.subscribe().await;

    assert!(result2.is_ok(), "Expected successful subscription after retries");

    let success_calls2 = retry_success_capture2.get_calls();
    assert_eq!(success_calls2.len(), 1, "Exactly 1 success callback should be called for retry success");

    let success_call = &success_calls2[0];
    assert_eq!(success_call.endpoint, retry_server.endpoint(), "Endpoint should match");
    assert!(success_call.attempt > 0, "Attempt number should be positive");
    assert!(success_call.elapsed_time.as_millis() > 0, "Elapsed time should be positive");

    retry_server.stop().await;
}

#[tokio::test]
async fn test_k3_failure_callback_final_state() {
    let failure_capture = CallbackCapture::<FailureCallbackData>::new();

    let config = MockServerConfig::failure_config();
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let client_config = ShredstreamClientConfig {
        retry_config: Some(RetryConfig { enabled: true, max_attempts: 3, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }),
        failure_callback: Some(failure_capture.create_failure_callback()),
        ..Default::default()
    };

    let mut client = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(client_config));

    let result = client.subscribe().await;

    assert!(result.is_err(), "Expected subscription failure");

    let failure_calls = failure_capture.get_calls();
    assert!(failure_calls.len() >= 1, "Expected at least 1 failure callback");

    let failure_call = &failure_calls[failure_calls.len() - 1];
    assert_eq!(failure_call.endpoint, server.endpoint(), "Endpoint should match");
    assert!(!failure_call.error_message.is_empty(), "Error message should not be empty");

    server.stop().await;
}

#[tokio::test]
async fn test_k4_circuit_breaker_failure_callback() {
    let failure_capture = CallbackCapture::<FailureCallbackData>::new();

    let config = MockServerConfig::circuit_breaker_trigger_config(6);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let client_config = ShredstreamClientConfig {
        retry_config: Some(RetryConfig { enabled: true, max_attempts: 3, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }),
        circuit_breaker_config: Some(CircuitBreakerConfig { failure_threshold: 5, time_window: Duration::from_secs(60), recovery_timeout: Duration::from_millis(100) }),
        failure_callback: Some(failure_capture.create_failure_callback()),
        ..Default::default()
    };

    let mut client = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(client_config));

    let result = client.subscribe().await;

    assert!(result.is_err(), "Expected circuit breaker failure");

    let failure_calls = failure_capture.get_calls();
    assert!(failure_calls.len() >= 1, "Expected at least 1 failure callback");

    server.stop().await;
}

#[tokio::test]
async fn test_k5_callback_execution_timing() {
    let disconnect_capture = CallbackCapture::<DisconnectCallbackData>::new();
    let retry_attempt_capture = CallbackCapture::<RetryAttemptCallbackData>::new();

    let config = MockServerConfig::multiple_failures_then_success_config(2);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let client_config = ShredstreamClientConfig {
        retry_config: Some(RetryConfig { enabled: true, max_attempts: 5, initial_delay: Duration::from_millis(50), max_delay: Duration::from_millis(200), multiplier: 2.0, auto_reset: true }),
        disconnect_callback: Some(disconnect_capture.create_disconnect_callback()),
        retry_attempt_callback: Some(retry_attempt_capture.create_retry_attempt_callback()),
        ..Default::default()
    };

    let start_time = std::time::Instant::now();

    let mut client = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(client_config));

    let result = client.subscribe().await;
    let total_time = start_time.elapsed();

    assert!(result.is_ok(), "Expected successful subscription");

    let disconnect_calls = disconnect_capture.get_calls();
    let retry_calls = retry_attempt_capture.get_calls();

    assert!(disconnect_calls.len() >= 2, "Expected disconnect callbacks");
    assert!(retry_calls.len() >= 2, "Expected retry attempt callbacks");

    assert!(total_time.as_millis() >= 100, "Total time should include retry delays");

    server.stop().await;
}
