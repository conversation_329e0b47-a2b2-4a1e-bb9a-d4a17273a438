use std::{
    sync::{<PERSON>, Mutex},
    time::Duration,
};

use shreder::common::client::{
    ShredstreamClient, ShredstreamClientConfig, ShredstreamSubscriptionFilters,
    config::{CircuitBreakerConfig, RetryConfig},
    errors::ShredstreamError,
};

pub struct CallbackCapture<T> {
    calls: Arc<Mutex<Vec<T>>>,
}

impl<T> CallbackCapture<T> {
    pub fn new() -> Self {
        Self { calls: Arc::new(Mutex::new(Vec::new())) }
    }

    pub fn get_calls(&self) -> Vec<T>
    where T: Clone {
        self.calls.lock().unwrap().clone()
    }

    pub fn call_count(&self) -> usize {
        self.calls.lock().unwrap().len()
    }

    pub fn clear(&self) {
        self.calls.lock().unwrap().clear();
    }
}

#[derive(Debug, <PERSON>lone)]
pub struct DisconnectCallbackData {
    pub endpoint: String,
    pub error_message: String,
    pub delay: Duration,
    pub attempt: u32,
}

impl CallbackCapture<DisconnectCallbackData> {
    pub fn create_disconnect_callback(&self) -> Box<dyn Fn(&str, &ShredstreamError, Duration, u32) + Send + Sync> {
        let calls = self.calls.clone();
        Box::new(move |endpoint, error, delay, attempt| {
            let data = DisconnectCallbackData { endpoint: endpoint.to_string(), error_message: error.to_string(), delay, attempt };
            calls.lock().unwrap().push(data);
        })
    }
}

#[derive(Debug, Clone)]
pub struct RetryAttemptCallbackData {
    pub endpoint: String,
    pub attempt: u32,
    pub max_attempts: u32,
}

impl CallbackCapture<RetryAttemptCallbackData> {
    pub fn create_retry_attempt_callback(&self) -> Box<dyn Fn(&str, u32, u32) + Send + Sync> {
        let calls = self.calls.clone();
        Box::new(move |endpoint, attempt, max_attempts| {
            let data = RetryAttemptCallbackData { endpoint: endpoint.to_string(), attempt, max_attempts };
            calls.lock().unwrap().push(data);
        })
    }
}

#[derive(Debug, Clone)]
pub struct RetrySuccessCallbackData {
    pub endpoint: String,
    pub attempt: u32,
    pub elapsed_time: Duration,
}

impl CallbackCapture<RetrySuccessCallbackData> {
    pub fn create_retry_success_callback(&self) -> Box<dyn Fn(&str, u32, Duration) + Send + Sync> {
        let calls = self.calls.clone();
        Box::new(move |endpoint, attempt, elapsed_time| {
            let data = RetrySuccessCallbackData { endpoint: endpoint.to_string(), attempt, elapsed_time };
            calls.lock().unwrap().push(data);
        })
    }
}

#[derive(Debug, Clone)]
pub struct FailureCallbackData {
    pub endpoint: String,
    pub error_message: String,
}

impl CallbackCapture<FailureCallbackData> {
    pub fn create_failure_callback(&self) -> Box<dyn Fn(&str, &ShredstreamError) + Send + Sync> {
        let calls = self.calls.clone();
        Box::new(move |endpoint, error| {
            let data = FailureCallbackData { endpoint: endpoint.to_string(), error_message: error.to_string() };
            calls.lock().unwrap().push(data);
        })
    }
}

pub fn create_test_client_with_retry(max_attempts: u32) -> ShredstreamClient {
    let config = ShredstreamClientConfig {
        connect_timeout: Some(Duration::from_millis(100)),
        subscribe_timeout: Some(Duration::from_millis(100)),
        retry_config: Some(RetryConfig { enabled: true, auto_reset: true, max_attempts, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0 }),
        circuit_breaker_config: Some(CircuitBreakerConfig::default()),
        failure_callback: None,
        disconnect_callback: None,
        retry_attempt_callback: None,
        retry_success_callback: None,
    };

    ShredstreamClient::new("http://test.example.com:8080".to_string(), None, Some(config))
}

pub fn create_test_client_with_circuit_breaker(failure_threshold: u32) -> ShredstreamClient {
    let config = ShredstreamClientConfig {
        connect_timeout: Some(Duration::from_millis(100)),
        subscribe_timeout: Some(Duration::from_millis(100)),
        retry_config: Some(RetryConfig::default()),
        circuit_breaker_config: Some(CircuitBreakerConfig { failure_threshold, time_window: Duration::from_secs(60), recovery_timeout: Duration::from_millis(100) }),
        failure_callback: None,
        disconnect_callback: None,
        retry_attempt_callback: None,
        retry_success_callback: None,
    };

    ShredstreamClient::new("http://test.example.com:8080".to_string(), None, Some(config))
}

pub fn create_test_client_with_short_timeout() -> ShredstreamClient {
    let config = ShredstreamClientConfig {
        connect_timeout: Some(Duration::from_millis(50)),
        subscribe_timeout: Some(Duration::from_millis(50)),
        retry_config: Some(RetryConfig { enabled: true, auto_reset: true, max_attempts: 3, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0 }),
        circuit_breaker_config: Some(CircuitBreakerConfig::default()),
        failure_callback: None,
        disconnect_callback: None,
        retry_attempt_callback: None,
        retry_success_callback: None,
    };

    ShredstreamClient::new("http://test.example.com:8080".to_string(), None, Some(config))
}

pub fn create_test_client_without_retry() -> ShredstreamClient {
    let config = ShredstreamClientConfig {
        connect_timeout: Some(Duration::from_millis(100)),
        subscribe_timeout: Some(Duration::from_millis(100)),
        retry_config: Some(RetryConfig { enabled: false, ..Default::default() }),
        circuit_breaker_config: Some(CircuitBreakerConfig::default()),
        failure_callback: None,
        disconnect_callback: None,
        retry_attempt_callback: None,
        retry_success_callback: None,
    };

    ShredstreamClient::new("http://test.example.com:8080".to_string(), None, Some(config))
}

pub fn create_test_filters() -> Option<ShredstreamSubscriptionFilters> {
    Some(ShredstreamSubscriptionFilters { accounts: Some(vec!["6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_string()]) })
}

pub fn create_test_endpoint() -> String {
    "http://test.example.com:8080".to_string()
}
