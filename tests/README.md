# Mock Server for Shredstream gRPC Testing

This directory contains a comprehensive mock server implementation for testing the Shredstream gRPC service. The mock server simulates various scenarios including success cases, failures, timeouts, and network issues.

## Features

- **Multiple Behavior Modes**: Success, failure, timeout, slow response, intermittent failure, and gradual slowdown
- **Dynamic Port Allocation**: Automatically assigns available ports to avoid conflicts
- **Configurable Responses**: Customize entry count, delays, and error scenarios
- **Test Data Generation**: Realistic test data with configurable sizes and patterns
- **Helper Functions**: Easy-to-use utilities for common test scenarios

## Quick Start

```rust
use mock_server::utils::{start_success_server, start_failure_server};

#[tokio::test]
async fn test_basic_success() {
    let server = start_success_server(5, 100).await.unwrap();
    let endpoint = server.endpoint();
    
    // Use endpoint with your gRPC client
    let mut client = ShredstreamProxyClient::connect(endpoint).await.unwrap();
    
    // Your test logic here
    
    server.stop().await;
}
```

## Available Mock Behaviors

### Success Scenarios
- **Success**: Returns specified number of entries with configurable delays
- **Slow Response**: Delays before sending first entry to test timeout handling

### Failure Scenarios
- **Connection Failure**: Immediately returns connection error
- **Timeout After Connect**: Connects but times out before sending data
- **Timeout After Entries**: Sends some entries then times out
- **Invalid Response**: Sends malformed data

### Advanced Scenarios
- **Intermittent Failure**: Alternates between success and failure
- **Gradual Slowdown**: Progressively increases delay between entries

## Helper Functions

### Quick Start Functions
```rust
// Success with 10 entries, 50ms delay between entries
start_success_server(10, 50).await

// Immediate connection failure
start_failure_server().await

// Send 3 entries then timeout after 1 second
start_timeout_server(3, 1000).await

// Custom behavior
start_custom_server(MockBehavior::SlowResponse {
    delay_before_first_entry: Duration::from_millis(500)
}).await
```

### Configuration
```rust
let config = MockServerConfig::default()
    .with_behavior(MockBehavior::Success {
        entry_count: 20,
        delay_between_entries: Duration::from_millis(100),
    })
    .with_port(8080);  // Optional: specify port

let server = start_mock_server(config).await.unwrap();
```

## Test Data Generation

The `TestDataGenerator` provides various methods for creating test data:

```rust
// Single entry with specified slot and data size
let entry = TestDataGenerator::create_entry(1000, 1024);

// Realistic entry with structured data
let entry = TestDataGenerator::create_realistic_entry_data(1000);

// Multiple sequential entries
let entries = TestDataGenerator::create_sequential_realistic_entries(10, 1000);

// Test request with sample filters
let request = TestDataGenerator::create_test_request();
```

## Example Test Scenarios

### Testing Retry Logic
```rust
#[tokio::test]
async fn test_retry_on_failure() {
    let server = start_failure_server().await.unwrap();
    
    // Test that your client properly retries on connection failure
    // Verify retry count, backoff delays, etc.
    
    server.stop().await;
}
```

### Testing Timeout Handling
```rust
#[tokio::test]
async fn test_timeout_handling() {
    let server = start_timeout_server(2, 500).await.unwrap();
    
    // Test that your client handles timeouts correctly
    // Verify it receives some entries before timeout
    
    server.stop().await;
}
```

### Testing Circuit Breaker
```rust
#[tokio::test]
async fn test_circuit_breaker() {
    let behavior = MockBehavior::IntermittentFailure {
        success_count: 2,
        failure_count: 3,
        cycle_repeat: true,
    };
    
    let server = start_custom_server(behavior).await.unwrap();
    
    // Test circuit breaker activation after consecutive failures
    
    server.stop().await;
}
```

## Running Tests

```bash
# Run all mock server tests
cargo test mock_server

# Run integration tests
cargo test --test integration_test

# Run specific test
cargo test test_mock_server_success_scenario
```

## Architecture

- **`config.rs`**: Defines mock behavior configurations
- **`server.rs`**: Core mock server implementation
- **`data.rs`**: Test data generation utilities
- **`utils.rs`**: Helper functions for starting servers
- **`integration_test.rs`**: Example integration tests

## Best Practices

1. **Always call `server.stop().await`** at the end of tests to clean up resources
2. **Use dynamic ports** (don't specify port) to avoid conflicts in parallel tests
3. **Test both success and failure scenarios** for comprehensive coverage
4. **Use realistic delays** that match your actual network conditions
5. **Verify specific error codes and messages** in failure scenarios

## Extending the Mock Server

To add new behavior modes:

1. Add new variant to `MockBehavior` enum in `config.rs`
2. Implement the behavior in `subscribe_entries` method in `server.rs`
3. Add helper function in `utils.rs` if needed
4. Create integration test to verify the behavior

The mock server is designed to be easily extensible for new test scenarios as your testing needs evolve.
