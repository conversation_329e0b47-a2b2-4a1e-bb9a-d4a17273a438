use std::time::Duration;

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub enum MockBehavior {
    Success { entry_count: usize, delay_between_entries: Duration },
    ConnectionFailure,
    TimeoutAfterConnect,
    TimeoutAfterEntries { entry_count: usize, delay_before_timeout: Duration },
    InvalidResponse,
    SlowResponse { delay_before_first_entry: Duration },
    IntermittentFailure { success_count: usize, failure_count: usize, cycle_repeat: bool },
    GradualSlowdown { initial_delay: Duration, delay_increment: Duration, max_delay: Duration },
    ConnectSuccessThenSubscribeFail,
    RapidDisconnect { connect_duration: Duration },
    MultipleFailuresThenSuccess { failure_count: usize },
    CircuitBreakerTrigger { rapid_failures: usize },
}

impl Default for MockBehavior {
    fn default() -> Self {
        Self::Success { entry_count: 10, delay_between_entries: Duration::from_millis(100) }
    }
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct MockServerConfig {
    pub behavior: MockBehavior,
    pub port: Option<u16>,
    pub bind_address: String,
}

impl Default for MockServerConfig {
    fn default() -> Self {
        Self { behavior: MockBehavior::default(), port: None, bind_address: "127.0.0.1".to_string() }
    }
}

impl MockServerConfig {
    pub fn with_behavior(mut self, behavior: MockBehavior) -> Self {
        self.behavior = behavior;
        self
    }

    pub fn with_port(mut self, port: u16) -> Self {
        self.port = Some(port);
        self
    }

    pub fn with_bind_address(mut self, address: String) -> Self {
        self.bind_address = address;
        self
    }

    pub fn success_config(entry_count: usize, delay_ms: u64) -> Self {
        Self::default().with_behavior(MockBehavior::Success { entry_count, delay_between_entries: Duration::from_millis(delay_ms) })
    }

    pub fn failure_config() -> Self {
        Self::default().with_behavior(MockBehavior::ConnectionFailure)
    }

    pub fn timeout_config(entry_count: usize, timeout_delay_ms: u64) -> Self {
        Self::default().with_behavior(MockBehavior::TimeoutAfterEntries { entry_count, delay_before_timeout: Duration::from_millis(timeout_delay_ms) })
    }

    pub fn slow_response_config(delay_ms: u64) -> Self {
        Self::default().with_behavior(MockBehavior::SlowResponse { delay_before_first_entry: Duration::from_millis(delay_ms) })
    }

    pub fn intermittent_failure_config(success_count: usize, failure_count: usize) -> Self {
        Self::default().with_behavior(MockBehavior::IntermittentFailure { success_count, failure_count, cycle_repeat: true })
    }

    pub fn connect_success_then_subscribe_fail_config() -> Self {
        Self::default().with_behavior(MockBehavior::ConnectSuccessThenSubscribeFail)
    }

    pub fn rapid_disconnect_config(connect_duration_ms: u64) -> Self {
        Self::default().with_behavior(MockBehavior::RapidDisconnect { connect_duration: Duration::from_millis(connect_duration_ms) })
    }

    pub fn multiple_failures_then_success_config(failure_count: usize) -> Self {
        Self::default().with_behavior(MockBehavior::MultipleFailuresThenSuccess { failure_count })
    }

    pub fn circuit_breaker_trigger_config(rapid_failures: usize) -> Self {
        Self::default().with_behavior(MockBehavior::CircuitBreakerTrigger { rapid_failures })
    }
}
