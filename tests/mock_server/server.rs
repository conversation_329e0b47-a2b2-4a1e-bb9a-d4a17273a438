use std::{
    net::SocketAddr,
    pin::Pin,
    sync::{Arc, Mutex},
    time::Duration,
};

use futures_util::Stream;
use shreder::generated::{Entry, SubscribeEntriesRequest, shredstream_proxy_server::ShredstreamProxy};
use tokio::{sync::mpsc, time::sleep};
use tokio_stream::wrappers::ReceiverStream;
use tonic::{Request, Response, Status};

use super::{MockServerConfig, TestDataGenerator, config::MockBehavior};

#[derive(Debug)]
pub struct MockShredstreamServer {
    config: Arc<Mutex<MockServerConfig>>,
    request_count: Arc<Mutex<usize>>,
}

impl MockShredstreamServer {
    pub fn new(config: MockServerConfig) -> Self {
        Self { config: Arc::new(Mutex::new(config)), request_count: Arc::new(Mutex::new(0)) }
    }

    pub fn update_config(&self, new_config: MockServerConfig) {
        let mut config = self.config.lock().unwrap();
        *config = new_config;
    }

    pub fn get_request_count(&self) -> usize {
        *self.request_count.lock().unwrap()
    }

    pub fn reset_request_count(&self) {
        let mut count = self.request_count.lock().unwrap();
        *count = 0;
    }
}

#[tonic::async_trait]
impl ShredstreamProxy for MockShredstreamServer {
    type SubscribeEntriesStream = Pin<Box<dyn Stream<Item = Result<Entry, Status>> + Send>>;

    async fn subscribe_entries(&self, _request: Request<SubscribeEntriesRequest>) -> Result<Response<Self::SubscribeEntriesStream>, Status> {
        {
            let mut count = self.request_count.lock().unwrap();
            *count += 1;
        }

        let config = {
            let config_guard = self.config.lock().unwrap();
            config_guard.clone()
        };

        let request_count = self.request_count.clone();
        let (tx, rx) = mpsc::channel(128);

        tokio::spawn(async move {
            match config.behavior {
                MockBehavior::Success { entry_count, delay_between_entries } => {
                    let entries = TestDataGenerator::create_sequential_realistic_entries(entry_count, 1000);
                    for entry in entries {
                        if tx.send(Ok(entry)).await.is_err() {
                            break;
                        }
                        sleep(delay_between_entries).await;
                    }
                }

                MockBehavior::ConnectionFailure => {
                    let _ = tx.send(Err(Status::unavailable("Connection failed"))).await;
                }

                MockBehavior::TimeoutAfterConnect => {
                    sleep(Duration::from_secs(30)).await;
                    let _ = tx.send(Err(Status::deadline_exceeded("Request timeout"))).await;
                }

                MockBehavior::TimeoutAfterEntries { entry_count, delay_before_timeout } => {
                    let entries = TestDataGenerator::create_sequential_realistic_entries(entry_count, 1000);
                    for entry in entries {
                        if tx.send(Ok(entry)).await.is_err() {
                            break;
                        }
                        sleep(Duration::from_millis(50)).await;
                    }
                    sleep(delay_before_timeout).await;
                    let _ = tx.send(Err(Status::deadline_exceeded("Stream timeout"))).await;
                }

                MockBehavior::InvalidResponse => {
                    let invalid_entry = Entry { slot: u64::MAX, entries: vec![] };
                    let _ = tx.send(Ok(invalid_entry)).await;
                    let _ = tx.send(Err(Status::internal("Invalid response data"))).await;
                }

                MockBehavior::SlowResponse { delay_before_first_entry } => {
                    sleep(delay_before_first_entry).await;
                    let entries = TestDataGenerator::create_sequential_realistic_entries(5, 1000);
                    for entry in entries {
                        if tx.send(Ok(entry)).await.is_err() {
                            break;
                        }
                        sleep(Duration::from_millis(100)).await;
                    }
                }

                MockBehavior::IntermittentFailure { success_count, failure_count, cycle_repeat } => {
                    let mut cycle = 0;
                    loop {
                        for i in 0..success_count {
                            let entry = TestDataGenerator::create_realistic_entry_data(1000 + i as u64);
                            if tx.send(Ok(entry)).await.is_err() {
                                return;
                            }
                            sleep(Duration::from_millis(50)).await;
                        }

                        for _ in 0..failure_count {
                            let _ = tx.send(Err(Status::internal("Intermittent failure"))).await;
                            sleep(Duration::from_millis(50)).await;
                        }

                        cycle += 1;
                        if !cycle_repeat || cycle >= 3 {
                            break;
                        }
                    }
                }

                MockBehavior::GradualSlowdown { initial_delay, delay_increment, max_delay } => {
                    let mut current_delay = initial_delay;
                    let entries = TestDataGenerator::create_sequential_realistic_entries(10, 1000);

                    for entry in entries {
                        if tx.send(Ok(entry)).await.is_err() {
                            break;
                        }
                        sleep(current_delay).await;

                        current_delay = std::cmp::min(current_delay + delay_increment, max_delay);
                    }
                }

                MockBehavior::ConnectSuccessThenSubscribeFail => {
                    let _ = tx.send(Err(Status::internal("Subscribe failed after connect"))).await;
                }

                MockBehavior::RapidDisconnect { connect_duration } => {
                    sleep(connect_duration).await;
                    let _ = tx.send(Err(Status::unavailable("Rapid disconnect"))).await;
                }

                MockBehavior::MultipleFailuresThenSuccess { failure_count } => {
                    let current_count = {
                        let count = request_count.lock().unwrap();
                        *count
                    };

                    if current_count <= failure_count {
                        let _ = tx.send(Err(Status::unavailable(format!("Failure attempt {}", current_count)))).await;
                    } else {
                        let entries = TestDataGenerator::create_sequential_realistic_entries(5, 1000);
                        for entry in entries {
                            if tx.send(Ok(entry)).await.is_err() {
                                break;
                            }
                            sleep(Duration::from_millis(50)).await;
                        }
                    }
                }

                MockBehavior::CircuitBreakerTrigger { rapid_failures } => {
                    for i in 0..rapid_failures {
                        sleep(Duration::from_millis(100)).await;
                        let _ = tx.send(Err(Status::unavailable(format!("Rapid failure {}", i + 1)))).await;
                    }
                }
            }
        });

        let stream = ReceiverStream::new(rx);
        Ok(Response::new(Box::pin(stream)))
    }
}

pub struct MockServerHandle {
    pub address: SocketAddr,
    pub server_handle: tokio::task::JoinHandle<Result<(), tonic::transport::Error>>,
}

impl MockServerHandle {
    pub async fn stop(self) {
        self.server_handle.abort();
        let _ = self.server_handle.await;
    }

    pub fn endpoint(&self) -> String {
        format!("http://{}", self.address)
    }
}
