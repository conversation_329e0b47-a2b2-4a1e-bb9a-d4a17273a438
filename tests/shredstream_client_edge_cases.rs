use std::time::Duration;

use shreder::common::client::{
    ShredstreamClient, ShredstreamClientConfig,
    config::{CircuitBreakerConfig, RetryConfig},
    errors::ShredstreamError,
};

mod mock_server;
mod test_helpers;

use mock_server::{MockServerConfig, start_mock_server};
use test_helpers::*;

#[tokio::test]
async fn test_h1_zero_max_attempts() {
    let config = MockServerConfig::failure_config();
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let client_config = ShredstreamClientConfig { retry_config: Some(RetryConfig { enabled: true, max_attempts: 0, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }), ..Default::default() };

    let mut client = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(client_config));

    let result = client.subscribe().await;

    assert!(result.is_err(), "Expected immediate failure");

    match result.unwrap_err() {
        ShredstreamError::RetryExhausted(attempts) => {
            assert_eq!(attempts, 0, "Expected 0 retry attempts");
        }
        _ => panic!("Expected RetryExhausted error"),
    }

    server.stop().await;
}

#[tokio::test]
async fn test_h2_maximum_delay_boundary() {
    let config = MockServerConfig::multiple_failures_then_success_config(5);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let client_config = ShredstreamClientConfig { retry_config: Some(RetryConfig { enabled: true, max_attempts: 6, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(50), multiplier: 10.0, auto_reset: true }), ..Default::default() };

    let mut client = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(client_config));

    let result = client.subscribe().await;

    assert!(result.is_ok(), "Expected successful subscription");

    server.stop().await;
}

#[tokio::test]
async fn test_h3_circuit_breaker_threshold_boundary() {
    let config = MockServerConfig::circuit_breaker_trigger_config(3);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let client_config = ShredstreamClientConfig {
        circuit_breaker_config: Some(CircuitBreakerConfig { failure_threshold: 3, time_window: Duration::from_secs(60), recovery_timeout: Duration::from_millis(100) }),
        retry_config: Some(RetryConfig { enabled: true, max_attempts: 5, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }),
        ..Default::default()
    };

    let mut client = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(client_config));

    let result = client.subscribe().await;

    assert!(result.is_err(), "Expected circuit breaker to trigger");

    server.stop().await;
}

#[tokio::test]
async fn test_i1_rapid_state_changes() {
    let success_config = MockServerConfig::success_config(3, 10);
    let success_server = start_mock_server(success_config).await.expect("Failed to start success server");

    let mut client = ShredstreamClient::new(success_server.endpoint(), create_test_filters(), None);

    let result1 = client.subscribe().await;
    assert!(result1.is_ok(), "First subscription should succeed");

    success_server.stop().await;

    let failure_config = MockServerConfig::failure_config();
    let failure_server = start_mock_server(failure_config).await.expect("Failed to start failure server");

    let mut client2 = ShredstreamClient::new(
        failure_server.endpoint(),
        create_test_filters(),
        Some(ShredstreamClientConfig { retry_config: Some(RetryConfig { enabled: true, max_attempts: 2, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }), ..Default::default() }),
    );

    let result2 = client2.subscribe().await;
    assert!(result2.is_err(), "Second subscription should fail");

    failure_server.stop().await;
}

#[tokio::test]
async fn test_i2_callback_execution_order() {
    let disconnect_capture = CallbackCapture::<DisconnectCallbackData>::new();
    let retry_attempt_capture = CallbackCapture::<RetryAttemptCallbackData>::new();
    let retry_success_capture = CallbackCapture::<RetrySuccessCallbackData>::new();

    let config = MockServerConfig::multiple_failures_then_success_config(2);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let client_config = ShredstreamClientConfig {
        retry_config: Some(RetryConfig { enabled: true, max_attempts: 5, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }),
        disconnect_callback: Some(disconnect_capture.create_disconnect_callback()),
        retry_attempt_callback: Some(retry_attempt_capture.create_retry_attempt_callback()),
        retry_success_callback: Some(retry_success_capture.create_retry_success_callback()),
        ..Default::default()
    };

    let mut client = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(client_config));

    let result = client.subscribe().await;

    assert!(result.is_ok(), "Expected successful subscription");

    let disconnect_calls = disconnect_capture.get_calls();
    let retry_calls = retry_attempt_capture.get_calls();
    let success_calls = retry_success_capture.get_calls();

    assert!(disconnect_calls.len() >= 2, "Expected disconnect callbacks");
    assert!(retry_calls.len() >= 2, "Expected retry attempt callbacks");
    assert_eq!(success_calls.len(), 1, "Expected exactly 1 success callback");

    server.stop().await;
}

#[tokio::test]
async fn test_j1_connection_cleanup_on_reset() {
    let config = MockServerConfig::connect_success_then_subscribe_fail_config();
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let client_config = ShredstreamClientConfig { retry_config: Some(RetryConfig { enabled: true, max_attempts: 2, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }), ..Default::default() };

    let mut client = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(client_config));

    let result = client.subscribe().await;

    assert!(result.is_err(), "Expected subscription failure");

    server.stop().await;
}

#[tokio::test]
async fn test_j2_memory_usage_during_long_retry_sequences() {
    let config = MockServerConfig::multiple_failures_then_success_config(10);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let client_config = ShredstreamClientConfig { retry_config: Some(RetryConfig { enabled: true, max_attempts: 15, initial_delay: Duration::from_millis(1), max_delay: Duration::from_millis(10), multiplier: 1.1, auto_reset: true }), ..Default::default() };

    let mut client = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(client_config));

    let result = client.subscribe().await;

    assert!(result.is_ok(), "Expected successful subscription after many retries");

    server.stop().await;
}
