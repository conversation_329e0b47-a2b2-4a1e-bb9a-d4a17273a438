use std::time::Duration;

use futures_util::StreamExt;
use shreder::generated::{SubscribeEntriesRequest, shredstream_proxy_client::ShredstreamProxyClient};
use tokio::time::timeout;

mod mock_server;

use mock_server::{
    config::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    utils::{start_custom_server, start_failure_server, start_success_server, start_timeout_server},
};

#[tokio::test]
async fn test_mock_server_success_scenario() {
    let server = start_success_server(5, 100).await.unwrap();
    let endpoint = server.endpoint();

    let mut client = ShredstreamProxyClient::connect(endpoint).await.unwrap();

    let request = SubscribeEntriesRequest { accounts: std::collections::HashMap::new(), transactions: std::collections::HashMap::new(), slots: std::collections::HashMap::new(), commitment: None };

    let response = client.subscribe_entries(request).await.unwrap();
    let mut stream = response.into_inner();

    let mut entry_count = 0;
    while let Some(entry_result) = stream.next().await {
        match entry_result {
            Ok(entry) => {
                entry_count += 1;
                assert!(entry.slot >= 1000);
                assert!(!entry.entries.is_empty());

                if entry_count >= 3 {
                    break;
                }
            }
            Err(e) => {
                panic!("Unexpected error: {}", e);
            }
        }
    }

    assert!(entry_count >= 3);
    server.stop().await;
}

#[tokio::test]
async fn test_mock_server_failure_scenario() {
    let server = start_failure_server().await.unwrap();
    let endpoint = server.endpoint();

    let mut client = ShredstreamProxyClient::connect(endpoint).await.unwrap();

    let request = SubscribeEntriesRequest { accounts: std::collections::HashMap::new(), transactions: std::collections::HashMap::new(), slots: std::collections::HashMap::new(), commitment: None };

    let response = client.subscribe_entries(request).await.unwrap();
    let mut stream = response.into_inner();

    let entry_result = stream.next().await;
    assert!(entry_result.is_some());

    match entry_result.unwrap() {
        Ok(_) => panic!("Expected error but got success"),
        Err(status) => {
            assert_eq!(status.code(), tonic::Code::Unavailable);
            assert!(status.message().contains("Connection failed"));
        }
    }

    server.stop().await;
}

#[tokio::test]
async fn test_mock_server_timeout_scenario() {
    let server = start_timeout_server(2, 500).await.unwrap();
    let endpoint = server.endpoint();

    let mut client = ShredstreamProxyClient::connect(endpoint).await.unwrap();

    let request = SubscribeEntriesRequest { accounts: std::collections::HashMap::new(), transactions: std::collections::HashMap::new(), slots: std::collections::HashMap::new(), commitment: None };

    let response = client.subscribe_entries(request).await.unwrap();
    let mut stream = response.into_inner();

    let mut entry_count = 0;
    let mut got_timeout = false;

    while let Some(entry_result) = stream.next().await {
        match entry_result {
            Ok(_entry) => {
                entry_count += 1;
            }
            Err(status) => {
                if status.code() == tonic::Code::DeadlineExceeded {
                    got_timeout = true;
                    break;
                }
            }
        }
    }

    assert_eq!(entry_count, 2);
    assert!(got_timeout);
    server.stop().await;
}

#[tokio::test]
async fn test_mock_server_slow_response() {
    let behavior = MockBehavior::SlowResponse { delay_before_first_entry: Duration::from_millis(200) };

    let server = start_custom_server(behavior).await.unwrap();
    let endpoint = server.endpoint();

    let mut client = ShredstreamProxyClient::connect(endpoint).await.unwrap();

    let request = SubscribeEntriesRequest { accounts: std::collections::HashMap::new(), transactions: std::collections::HashMap::new(), slots: std::collections::HashMap::new(), commitment: None };

    let start_time = std::time::Instant::now();
    let response = client.subscribe_entries(request).await.unwrap();
    let mut stream = response.into_inner();

    let first_entry = timeout(Duration::from_secs(1), stream.next()).await.unwrap();
    let elapsed = start_time.elapsed();

    assert!(first_entry.is_some());
    assert!(elapsed >= Duration::from_millis(200));

    server.stop().await;
}

#[tokio::test]
async fn test_mock_server_intermittent_failure() {
    let behavior = MockBehavior::IntermittentFailure { success_count: 2, failure_count: 1, cycle_repeat: false };

    let server = start_custom_server(behavior).await.unwrap();
    let endpoint = server.endpoint();

    let mut client = ShredstreamProxyClient::connect(endpoint).await.unwrap();

    let request = SubscribeEntriesRequest { accounts: std::collections::HashMap::new(), transactions: std::collections::HashMap::new(), slots: std::collections::HashMap::new(), commitment: None };

    let response = client.subscribe_entries(request).await.unwrap();
    let mut stream = response.into_inner();

    let mut success_count = 0;
    let mut error_count = 0;

    while let Some(entry_result) = stream.next().await {
        match entry_result {
            Ok(_) => success_count += 1,
            Err(_) => error_count += 1,
        }

        if success_count + error_count >= 3 {
            break;
        }
    }

    assert_eq!(success_count, 2);
    assert_eq!(error_count, 1);
    server.stop().await;
}

#[tokio::test]
async fn test_mock_server_gradual_slowdown() {
    let behavior = MockBehavior::GradualSlowdown { initial_delay: Duration::from_millis(10), delay_increment: Duration::from_millis(20), max_delay: Duration::from_millis(100) };

    let server = start_custom_server(behavior).await.unwrap();
    let endpoint = server.endpoint();

    let mut client = ShredstreamProxyClient::connect(endpoint).await.unwrap();

    let request = SubscribeEntriesRequest { accounts: std::collections::HashMap::new(), transactions: std::collections::HashMap::new(), slots: std::collections::HashMap::new(), commitment: None };

    let response = client.subscribe_entries(request).await.unwrap();
    let mut stream = response.into_inner();

    let mut entry_times = Vec::new();
    let start_time = std::time::Instant::now();

    while let Some(entry_result) = stream.next().await {
        if let Ok(_entry) = entry_result {
            entry_times.push(start_time.elapsed());

            if entry_times.len() >= 3 {
                break;
            }
        }
    }

    assert_eq!(entry_times.len(), 3);

    for i in 1..entry_times.len() {
        let time_diff = entry_times[i] - entry_times[i - 1];
        assert!(time_diff >= Duration::from_millis(10));
    }

    server.stop().await;
}
