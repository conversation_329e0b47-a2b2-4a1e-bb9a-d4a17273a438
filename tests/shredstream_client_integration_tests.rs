use std::time::Duration;

use shreder::common::client::{
    ShredstreamClient, ShredstreamClientConfig,
    config::{CircuitBreakerConfig, RetryConfig},
    errors::ShredstreamError,
};

mod mock_server;
mod test_helpers;

use mock_server::{MockServerConfig, start_mock_server};
use test_helpers::*;

#[tokio::test]
async fn test_e1_single_successful_subscribe() {
    let config = MockServerConfig::success_config(5, 50);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let mut client = ShredstreamClient::new(server.endpoint(), create_test_filters(), None);

    let result = client.subscribe().await;

    assert!(result.is_ok(), "Expected successful subscription");

    server.stop().await;
}

#[tokio::test]
async fn test_e2_retry_success_after_failures() {
    let disconnect_capture = CallbackCapture::<DisconnectCallbackData>::new();
    let retry_attempt_capture = CallbackCapture::<RetryAttemptCallbackData>::new();
    let retry_success_capture = CallbackCapture::<RetrySuccessCallbackData>::new();

    // Use an invalid endpoint to test retry logic
    let invalid_endpoint = "http://127.0.0.1:1".to_string(); // Port 1 should be unavailable

    let client_config = ShredstreamClientConfig {
        connect_timeout: Some(Duration::from_millis(100)),
        subscribe_timeout: Some(Duration::from_millis(100)),
        retry_config: Some(RetryConfig { enabled: true, auto_reset: true, max_attempts: 3, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0 }),
        circuit_breaker_config: Some(CircuitBreakerConfig::default()),
        disconnect_callback: Some(disconnect_capture.create_disconnect_callback()),
        retry_attempt_callback: Some(retry_attempt_capture.create_retry_attempt_callback()),
        retry_success_callback: Some(retry_success_capture.create_retry_success_callback()),
        failure_callback: None,
    };

    let mut client = ShredstreamClient::new(invalid_endpoint, create_test_filters(), Some(client_config));

    let result = client.subscribe().await;

    // This should fail after retries
    assert!(result.is_err(), "Expected failure after retries");

    let disconnect_calls = disconnect_capture.get_calls();
    println!("Disconnect calls: {}", disconnect_calls.len());

    let retry_calls = retry_attempt_capture.get_calls();
    println!("Retry calls: {}", retry_calls.len());

    let success_calls = retry_success_capture.get_calls();
    println!("Success calls: {}", success_calls.len());

    // For now, just check that we got some callbacks
    assert!(disconnect_calls.len() >= 1, "Expected at least 1 disconnect callback");
}

#[tokio::test]
async fn test_e3_success_without_retry_disabled() {
    let config = MockServerConfig::success_config(3, 50);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let mut client = create_test_client_without_retry();
    let client_with_endpoint = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(ShredstreamClientConfig { retry_config: Some(RetryConfig { enabled: false, ..Default::default() }), ..Default::default() }));

    let mut client = client_with_endpoint;
    let result = client.subscribe().await;

    assert!(result.is_ok(), "Expected successful subscription without retry");

    server.stop().await;
}

#[tokio::test]
async fn test_f1_connect_timeout() {
    let config = MockServerConfig::timeout_config(0, 200);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let mut client = create_test_client_with_short_timeout();
    let client_with_endpoint = ShredstreamClient::new(
        server.endpoint(),
        create_test_filters(),
        Some(ShredstreamClientConfig {
            connect_timeout: Some(Duration::from_millis(50)),
            subscribe_timeout: Some(Duration::from_millis(50)),
            retry_config: Some(RetryConfig { enabled: true, max_attempts: 2, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }),
            ..Default::default()
        }),
    );

    let mut client = client_with_endpoint;
    let result = client.subscribe().await;

    assert!(result.is_err(), "Expected timeout error");

    match result.unwrap_err() {
        ShredstreamError::RetryExhausted(attempts) => {
            assert_eq!(attempts, 2, "Expected 2 retry attempts");
        }
        _ => panic!("Expected RetryExhausted error"),
    }

    server.stop().await;
}

#[tokio::test]
async fn test_f2_subscribe_timeout() {
    let config = MockServerConfig::timeout_config(0, 200);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let mut client = create_test_client_with_short_timeout();
    let client_with_endpoint = ShredstreamClient::new(
        server.endpoint(),
        create_test_filters(),
        Some(ShredstreamClientConfig {
            connect_timeout: Some(Duration::from_millis(100)),
            subscribe_timeout: Some(Duration::from_millis(50)),
            retry_config: Some(RetryConfig { enabled: true, max_attempts: 2, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }),
            ..Default::default()
        }),
    );

    let mut client = client_with_endpoint;
    let result = client.subscribe().await;

    assert!(result.is_err(), "Expected timeout error");

    server.stop().await;
}

#[tokio::test]
async fn test_f3_timeout_during_retry() {
    let config = MockServerConfig::timeout_config(0, 200);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let mut client = create_test_client_with_short_timeout();
    let client_with_endpoint = ShredstreamClient::new(
        server.endpoint(),
        create_test_filters(),
        Some(ShredstreamClientConfig {
            connect_timeout: Some(Duration::from_millis(50)),
            subscribe_timeout: Some(Duration::from_millis(50)),
            retry_config: Some(RetryConfig { enabled: true, max_attempts: 3, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }),
            ..Default::default()
        }),
    );

    let mut client = client_with_endpoint;
    let result = client.subscribe().await;

    assert!(result.is_err(), "Expected timeout error");

    match result.unwrap_err() {
        ShredstreamError::RetryExhausted(attempts) => {
            assert_eq!(attempts, 3, "Expected 3 retry attempts");
        }
        _ => panic!("Expected RetryExhausted error"),
    }

    server.stop().await;
}

#[tokio::test]
async fn test_g1_error_priority_retry_vs_circuit_breaker() {
    let config = MockServerConfig::circuit_breaker_trigger_config(6);
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let mut client = create_test_client_with_circuit_breaker(5);
    let client_with_endpoint = ShredstreamClient::new(
        server.endpoint(),
        create_test_filters(),
        Some(ShredstreamClientConfig {
            retry_config: Some(RetryConfig { enabled: true, max_attempts: 3, initial_delay: Duration::from_millis(10), max_delay: Duration::from_millis(100), multiplier: 2.0, auto_reset: true }),
            circuit_breaker_config: Some(CircuitBreakerConfig { failure_threshold: 5, time_window: Duration::from_secs(60), recovery_timeout: Duration::from_millis(100) }),
            ..Default::default()
        }),
    );

    let mut client = client_with_endpoint;
    let result = client.subscribe().await;

    assert!(result.is_err(), "Expected error");

    server.stop().await;
}

#[tokio::test]
async fn test_g2_error_propagation_without_retry() {
    let config = MockServerConfig::failure_config();
    let server = start_mock_server(config).await.expect("Failed to start mock server");

    let mut client = create_test_client_without_retry();
    let client_with_endpoint = ShredstreamClient::new(server.endpoint(), create_test_filters(), Some(ShredstreamClientConfig { retry_config: Some(RetryConfig { enabled: false, ..Default::default() }), ..Default::default() }));

    let mut client = client_with_endpoint;
    let result = client.subscribe().await;

    assert!(result.is_err(), "Expected connection failure");

    server.stop().await;
}
